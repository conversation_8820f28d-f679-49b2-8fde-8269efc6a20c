import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { getAuthTokenFromRequest } from '@/utils/auth';
import * as fs from 'fs';
import { join } from 'path';
import { getImagePath } from '@/utils/imagePathUtils';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const packageIdParam = url.searchParams.get('packageId');
    const providerId = url.searchParams.get('providerId');
    const page = +url.searchParams.get('page')! || 1;
    const limit = +url.searchParams.get('limit')! || 10;
    const offset = (page - 1) * limit;
    const includeInactive = url.searchParams.get('includeInactive') === 'true';

    // Build the WHERE clause based on providerId and includeInactive parameters
    let whereClause = '';

    if (providerId) {
      whereClause = `WHERE sp.provider_id = ${parseInt(providerId)}`;
      if (!includeInactive) {
        whereClause += ' AND sp.is_active = TRUE';
      }
    } else if (!includeInactive) {
      whereClause = 'WHERE sp.is_active = TRUE';
    }

    if (packageIdParam) {
      return getPackageById(+packageIdParam, providerId || undefined);
    }

    // Check if conditions column exists
    let hasConditionsColumn = false;
    try {
      const columnCheck = await query(`
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'service_packages'
        AND COLUMN_NAME = 'conditions'
      `) as any[];
      hasConditionsColumn = columnCheck.length > 0;
    } catch (error) {
      // If column check fails, assume column doesn't exist
      hasConditionsColumn = false;
    }

    const conditionsField = hasConditionsColumn ? 'sp.conditions,' : "'' as conditions,";

    const rows = (await query(
      `
      SELECT
        sp.package_id as id,
        sp.name,
        sp.description,
        sp.category,
        sp.cremation_type    AS cremationType,
        sp.processing_time   AS processingTime,
        sp.price,
        ${conditionsField}
        sp.is_active         AS isActive,
        svp.provider_id      AS providerId,
        svp.name             AS providerName
      FROM service_packages sp
      JOIN service_providers svp
        ON sp.provider_id = svp.provider_id
      ${whereClause}
      ORDER BY sp.created_at DESC
      LIMIT ? OFFSET ?
      `,
      [limit, offset]
    )) as any[];

    const countRows = (await query(
      `
      SELECT COUNT(*) AS total
      FROM service_packages sp
      ${whereClause}
      `
    )) as any[];
    const total = +(countRows[0]?.total || 0);

    const packages = await enhancePackagesWithDetails(rows);

    return NextResponse.json({
      packages,
      pagination: { page, limit, total, totalPages: Math.ceil(total / limit) },
    });
  } catch (err) {
    console.error(err);
    return NextResponse.json(
      { error: 'Failed to fetch packages', details: (err as Error).message },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const authToken = getAuthTokenFromRequest(request);
    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const [userId, accountType] = authToken.split('_');
    if (accountType !== 'business') {
      return NextResponse.json(
        { error: 'Only business accounts can create packages' },
        { status: 403 }
      );
    }

    const prov = (await query(
      'SELECT provider_id as id FROM service_providers WHERE user_id = ?',
      [userId]
    )) as any[];
    if (prov.length === 0) {
      return NextResponse.json({ error: 'Service provider not found' }, { status: 404 });
    }
    const providerId = prov[0].id;

    const {
      name,
      description,
      category,
      cremationType,
      processingTime,
      price,
      conditions,
      inclusions = [],
      addOns = [],
      images = [],
    } = await request.json();

    if (!name || !description || price == null) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    await query('START TRANSACTION');
    try {
      // Insert package - conditions column doesn't exist in current schema
      const insertQuery = `
        INSERT INTO service_packages
          (provider_id, name, description, category, cremation_type,
           processing_time, price, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, TRUE)
      `;
      const insertParams = [providerId, name, description, category, cremationType, processingTime, price];

      const pkgRes = (await query(insertQuery, insertParams)) as any;
      const packageId = pkgRes.insertId;

      // inclusions
      for (const incDesc of inclusions.filter((x: any) => x)) {
        await query(
          'INSERT INTO package_inclusions (package_id, description) VALUES (?, ?)',
          [packageId, incDesc]
        );
      }

      // add-ons
      for (const raw of addOns) {
        let desc: string;
        let cost: number | null = null;

        if (typeof raw === 'string') {
          desc = raw.replace(/\s*\(\+₱[\d,]+\)/, '').trim();
          const m = raw.match(/\(\+₱([\d,]+)\)/);
          cost = m ? +m[1].replace(/,/g, '') : null;
        } else {
          desc = raw.name;
          const num = parseFloat(String(raw.price));
          cost = isNaN(num) ? null : num;
        }
        if (!desc) continue;

        // The package_addons table uses addon_id as auto_increment primary key
        await query(
          'INSERT INTO package_addons (package_id, description, price) VALUES (?, ?, ?)',
          [packageId, desc, cost]
        );
      }

      // images
      const moved = await moveImagesToPackageFolder(images, pkgRes.insertId);
      for (let i = 0; i < moved.length; i++) {
        await query(
          'INSERT INTO package_images (package_id, image_path) VALUES (?, ?)',
          [packageId, moved[i]]
        );
      }

      await query('COMMIT');
      return NextResponse.json({
        success: true,
        packageId,
        message: 'Package created successfully',
        images: moved,
      });
    } catch (innerErr) {
      await query('ROLLBACK');
      throw innerErr;
    }
  } catch (err) {
    console.error(err);
    return NextResponse.json(
      { error: 'Failed to create package', details: (err as Error).message },
      { status: 500 }
    );
  }
}

async function getPackageById(packageId: number, providerId?: string) {
  try {
    // Build the query with optional providerId filter
    let sql = `
      SELECT
        sp.*,
        svp.provider_id   AS providerId,
        svp.name AS providerName
      FROM service_packages sp
      JOIN service_providers svp
        ON sp.provider_id = svp.provider_id
      WHERE sp.package_id = ?
    `;

    const params = [packageId];

    // Add providerId filter if provided
    if (providerId) {
      sql += ` AND sp.provider_id = ?`;
      params.push(parseInt(providerId));
    }

    const rows = (await query(sql, params)) as any[];
    if (!rows.length) {
      return NextResponse.json({ error: 'Package not found' }, { status: 404 });
    }
    const [pkg] = await enhancePackagesWithDetails(rows);
    return NextResponse.json({ package: pkg });
  } catch (err) {
    console.error(err);
    return NextResponse.json(
      { error: 'Failed to fetch package', details: (err as Error).message },
      { status: 500 }
    );
  }
}

async function enhancePackagesWithDetails(pkgs: any[]) {
  if (!pkgs.length) return [];

  // Use the correct field name - the query selects package_id as id
  const ids = pkgs.map((p) => p.id || p.package_id);

  const [incs, adds, imgs] = await Promise.all([
    query(`SELECT package_id, description FROM package_inclusions WHERE package_id IN (?)`, [ids]),
    query(`SELECT package_id, addon_id as id, description, price FROM package_addons WHERE package_id IN (?)`, [ids]),
    query(`SELECT package_id, image_path FROM package_images WHERE package_id IN (?)`, [ids]),
  ]) as any[][];

  const groupBy = (arr: any[], key: string) =>
    arr.reduce((acc, cur) => {
      (acc[cur[key]] = acc[cur[key]] || []).push(cur);
      return acc;
    }, {} as Record<string, any[]>);

  const incMap = groupBy(incs, 'package_id');
  const addMap = groupBy(adds, 'package_id');
  const imgMap = groupBy(imgs, 'package_id');

  return pkgs.map((p: any) => {
    // Use the correct package ID field
    const packageId = p.id || p.package_id;

    const inclusions = (incMap[packageId] || []).map((i: any) => i.description);
    const addOns = (addMap[packageId] || []).map((a: any) => ({
      id: a.id,
      name: a.description,
      price: a.price != null ? +a.price : 0,
      displayText: a.price != null ? `${a.description} (+₱${(+a.price).toLocaleString()})` : a.description
    }));
    const images = (imgMap[packageId] || [])
      .map((i: any) => {
        const path = i.image_path;
        if (!path || path.startsWith('blob:')) return null;
        return path.startsWith('http') ? path : getImagePath(path);
      })
      .filter(Boolean);

    // Only add fallback image if no images exist and packageId is valid
    if (!images.length && packageId) {
      images.push(`/images/sample-package-${(packageId % 5) + 1}.svg`);
    }

    return { ...p, inclusions, addOns, images };
  });
}

async function moveImagesToPackageFolder(paths: string[], packageId: number) {
  if (!paths.length) return [];
  const base = join(process.cwd(), 'public', 'uploads', 'packages', String(packageId));
  if (!fs.existsSync(base)) fs.mkdirSync(base, { recursive: true });

  return Promise.all(
    paths.map((rel: string) => {
      if (rel.includes(`/uploads/packages/${packageId}/`)) return rel;
      const filename = rel.split('/').pop()!;
      const src = join(process.cwd(), 'public', rel);
      const destRel = `/uploads/packages/${packageId}/${filename}`;
      const dest = join(process.cwd(), 'public', destRel);
      if (!fs.existsSync(src)) return rel;
      fs.copyFileSync(src, dest);
      try { fs.unlinkSync(src); } catch {}
      return destRel;
    })
  );
}