import { NextRequest, NextResponse } from 'next/server';
import { getAuthTokenFromRequest } from '@/utils/auth';
import { query } from '@/lib/db';

// Import the email templates
import { createBookingStatusUpdateEmail } from '@/lib/emailTemplates';
// Import the consolidated email service
import { sendEmail } from '@/lib/consolidatedEmailService';

// Refund processing function
async function processRefund(bookingDetails: any) {
  console.log('Processing refund for booking:', bookingDetails.id);

  try {
    // Check if there are any payment transactions for this booking
    const paymentTransactions = await query(
      `SELECT * FROM payment_transactions WHERE booking_id = ? AND status = 'succeeded'`,
      [bookingDetails.id]
    ) as any[];

    if (paymentTransactions.length > 0) {
      // Process refund for each successful payment transaction
      for (const transaction of paymentTransactions) {
        if (transaction.payment_method === 'gcash' && transaction.provider === 'paymongo') {
          // For GCash payments through PayMongo, we would normally call their refund API
          // For now, we'll just mark it as refunded in our system
          await query(
            `UPDATE payment_transactions SET status = 'refunded', updated_at = NOW() WHERE id = ?`,
            [transaction.id]
          );

          console.log(`Marked PayMongo transaction ${transaction.id} as refunded`);
        } else if (transaction.payment_method === 'cash') {
          // For cash payments, mark as refunded (manual refund required)
          await query(
            `UPDATE payment_transactions SET status = 'refunded', updated_at = NOW() WHERE id = ?`,
            [transaction.id]
          );

          console.log(`Marked cash transaction ${transaction.id} as refunded (manual refund required)`);
        }
      }
    }

    // Create a refund record for tracking
    const refundAmount = bookingDetails.price || bookingDetails.total_price || 0;

    return {
      success: true,
      refund_amount: refundAmount,
      payment_method: bookingDetails.payment_method,
      message: bookingDetails.payment_method === 'cash'
        ? 'Cash refund will be processed manually by the service provider'
        : 'Refund has been initiated and will be processed within 3-5 business days'
    };
  } catch (error) {
    console.error('Refund processing error:', error);
    throw new Error('Failed to process refund');
  }
}

export async function POST(request: NextRequest) {
  // Extract ID from URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const bookingId = pathParts[pathParts.length - 2]; // -2 because the last part is 'cancel'
  try {
    // Get user ID from auth token
    const authToken = getAuthTokenFromRequest(request);
    if (!authToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const [userId, accountType] = authToken.split('_');
    if (!userId || accountType !== 'user') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!bookingId) {
      return NextResponse.json({ error: 'Booking ID is required' }, { status: 400 });
    }


    // Get booking details first to check payment status
    let bookingDetails = null;
    let needsRefund = false;

    try {
      // First try to get from service_bookings table
      const bookingResult = await query(
        `SELECT * FROM service_bookings WHERE id = ? AND user_id = ?`,
        [bookingId, userId]
      ) as any[];

      if (bookingResult && bookingResult.length > 0) {
        bookingDetails = bookingResult[0];
        needsRefund = bookingDetails.payment_status === 'paid';
      } else {
        // Try bookings table
        const bookingsResult = await query(
          `SELECT * FROM bookings WHERE id = ? AND user_id = ?`,
          [bookingId, userId]
        ) as any[];

        if (bookingsResult && bookingsResult.length > 0) {
          bookingDetails = bookingsResult[0];
          // For bookings table, assume needs refund if there's a total_price
          needsRefund = bookingDetails.total_price > 0;
        }
      }
    } catch (error) {
      console.error('Error fetching booking details:', error);
    }

    if (!bookingDetails) {
      return NextResponse.json({
        error: 'Booking not found or you do not have permission to cancel this booking'
      }, { status: 404 });
    }

    // Process refund if payment was made
    let refundResult = null;
    if (needsRefund) {
      try {
        refundResult = await processRefund(bookingDetails);
      } catch (refundError) {
        console.error('Refund processing failed:', refundError);
        // Continue with cancellation even if refund fails
      }
    }

    // Update the booking status in the database
    try {
      if (bookingDetails.hasOwnProperty('payment_status')) {
        // service_bookings table
        const updateResult = await query(
          `UPDATE service_bookings
           SET status = 'cancelled',
               payment_status = ?,
               updated_at = NOW()
           WHERE id = ? AND user_id = ?`,
          [needsRefund ? 'refunded' : bookingDetails.payment_status, bookingId, userId]
        );
      } else {
        // bookings table
        const updateResult = await query(
          `UPDATE bookings
           SET status = 'cancelled', updated_at = NOW()
           WHERE id = ? AND user_id = ?`,
          [bookingId, userId]
        );
      }
    } catch (dbError) {
      console.error('Database update failed:', dbError);
      return NextResponse.json({
        error: 'Failed to cancel booking'
      }, { status: 500 });
    }

    // Simulate a small delay to show loading state
    await new Promise(resolve => setTimeout(resolve, 500));

    // Send booking cancellation email
    try {

      // Get booking details from database (in a real app)
      // For now, we'll use mock data
      let bookingDetails;
      let userEmail = '';

      try {
        // Try to get real booking data and user email from database
        const bookingResult = await query(
          `SELECT b.*, u.email, u.first_name, u.last_name, 'N/A' as pet_name
           FROM bookings b
           JOIN users u ON b.user_id = u.user_id
           WHERE b.booking_id = ? AND b.user_id = ?`,
          [bookingId, userId]
        ) as any[];

        if (bookingResult && bookingResult.length > 0) {
          const booking = bookingResult[0];
          userEmail = booking.email;

          // Format date for email
          const bookingDate = new Date(booking.booking_date);
          const formattedDate = bookingDate.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });

          bookingDetails = {
            customerName: `${booking.first_name} ${booking.last_name}`,
            serviceName: booking.service_name,
            providerName: booking.provider_name,
            bookingDate: formattedDate,
            bookingTime: booking.booking_time,
            petName: booking.pet_name,
            bookingId: booking.id,
            status: 'cancelled',
            notes: 'Your booking has been cancelled as requested.'
          };
        }
      } catch (dbError) {
        // Continue with mock data if we can't get real data
      }

      // If we couldn't get real data, use mock data
      if (!bookingDetails) {
        // Get user email from database
        try {
          const userResult = await query('SELECT email, first_name, last_name FROM users WHERE user_id = ?', [userId]) as any[];
          if (userResult && userResult.length > 0) {
            userEmail = userResult[0].email;

            // Create mock booking details
            bookingDetails = {
              customerName: `${userResult[0].first_name} ${userResult[0].last_name}`,
              serviceName: 'Pet Memorial Service',
              providerName: 'Rainbow Paws Provider',
              bookingDate: new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }),
              bookingTime: '10:00 AM',
              petName: 'Your Pet',
              bookingId: bookingId,
              status: 'cancelled',
              notes: 'Your booking has been cancelled as requested.'
            };
          }
        } catch (userError) {
        }
      }

      // If we still don't have an email or booking details, use fallbacks
      if (!userEmail) {
        userEmail = '<EMAIL>';
      }

      if (!bookingDetails) {
        bookingDetails = {
          customerName: 'Valued Customer',
          serviceName: 'Pet Memorial Service',
          providerName: 'Rainbow Paws Provider',
          bookingDate: new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }),
          bookingTime: '10:00 AM',
          petName: 'Your Pet',
          bookingId: bookingId,
          status: 'cancelled',
          notes: 'Your booking has been cancelled as requested.'
        };
      }

      // Create email content using template
      const emailContent = createBookingStatusUpdateEmail(bookingDetails);

      // Send email using unified email service
      try {
        const emailResult = await sendEmail({
          to: userEmail,
          subject: emailContent.subject,
          html: emailContent.html
        });

        if (emailResult.success) {
        } else {
          // Continue with the cancellation process even if the email fails
        }
      } catch (emailSendError) {
        // Continue with the cancellation process even if the email fails
      }
    } catch (emailError) {
      // Continue with the cancellation process even if the email fails
    }

    return NextResponse.json({
      success: true,
      message: needsRefund
        ? 'Booking cancelled successfully and refund has been processed'
        : 'Booking cancelled successfully',
      booking: {
        id: bookingId,
        status: 'cancelled',
        payment_status: needsRefund ? 'refunded' : bookingDetails.payment_status
      },
      refund: refundResult
    });
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
